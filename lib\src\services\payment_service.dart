import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class PaymentService {
  static const String _baseUrl = 'https://api.rokhsati.yakoub-dev.h-s.cloud/api';
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  /// Create a payment with Chargily
  static Future<PaymentResponse> createPayment({
    required String requestId,
    required double amount,
    String currency = 'DZD',
  }) async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        throw PaymentException('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/payment/create'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'request_id': requestId,
          'amount': amount,
          'currency': currency,
        }),
      );

      print('Payment creation response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return PaymentResponse.fromJson(data);
      } else {
        final errorData = jsonDecode(response.body);
        throw PaymentException(
          errorData['message'] ?? 'Failed to create payment',
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }

  /// Handle payment return from Chargily
  static Future<PaymentStatus> handlePaymentReturn(String checkoutId) async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        throw PaymentException('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/payment/back?checkout_id=$checkoutId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      print('Payment return response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaymentStatus.fromJson(data);
      } else {
        final errorData = jsonDecode(response.body);
        throw PaymentException(
          errorData['message'] ?? 'Failed to verify payment',
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }

  /// Get payment status
  static Future<PaymentStatus> getPaymentStatus(String paymentId) async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        throw PaymentException('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/payment/status/$paymentId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaymentStatus.fromJson(data);
      } else {
        final errorData = jsonDecode(response.body);
        throw PaymentException(
          errorData['message'] ?? 'Failed to get payment status',
        );
      }
    } catch (e) {
      if (e is PaymentException) rethrow;
      throw PaymentException('Network error: ${e.toString()}');
    }
  }
}

/// Payment response model
class PaymentResponse {
  final String checkoutId;
  final String paymentUrl;
  final String status;
  final double amount;
  final String currency;

  PaymentResponse({
    required this.checkoutId,
    required this.paymentUrl,
    required this.status,
    required this.amount,
    required this.currency,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      checkoutId: json['checkout_id'] ?? json['data']?['checkout_id'] ?? '',
      paymentUrl: json['payment_url'] ?? json['data']?['payment_url'] ?? '',
      status: json['status'] ?? json['data']?['status'] ?? 'pending',
      amount: (json['amount'] ?? json['data']?['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? json['data']?['currency'] ?? 'DZD',
    );
  }
}

/// Payment status model
class PaymentStatus {
  final String paymentId;
  final String status;
  final double amount;
  final String currency;
  final DateTime? paidAt;

  PaymentStatus({
    required this.paymentId,
    required this.status,
    required this.amount,
    required this.currency,
    this.paidAt,
  });

  factory PaymentStatus.fromJson(Map<String, dynamic> json) {
    return PaymentStatus(
      paymentId: json['payment_id'] ?? json['data']?['payment_id'] ?? '',
      status: json['status'] ?? json['data']?['status'] ?? 'pending',
      amount: (json['amount'] ?? json['data']?['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? json['data']?['currency'] ?? 'DZD',
      paidAt: json['paid_at'] != null 
          ? DateTime.tryParse(json['paid_at']) 
          : json['data']?['paid_at'] != null
              ? DateTime.tryParse(json['data']['paid_at'])
              : null,
    );
  }

  bool get isPaid => status.toLowerCase() == 'paid' || status.toLowerCase() == 'completed';
  bool get isPending => status.toLowerCase() == 'pending';
  bool get isFailed => status.toLowerCase() == 'failed' || status.toLowerCase() == 'cancelled';
}

/// Payment exception class
class PaymentException implements Exception {
  final String message;
  
  PaymentException(this.message);
  
  @override
  String toString() => 'PaymentException: $message';
}
