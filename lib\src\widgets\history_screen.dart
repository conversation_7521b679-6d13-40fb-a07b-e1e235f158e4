import 'dart:convert';
import 'package:droit/src/widgets/AppealScreen.dart';
import 'package:droit/src/widgets/base_screen.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../config/colors.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import '../config/theme_helper.dart';
import '../services/payment_service.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  _HistoryScreenState createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _requests = [];
  bool _isLoading = true;
  String? _errorMessage;
  final _storage = const FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _fetchRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'no_token') ?? 'No authentication token found';
          _isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/requests'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );
      if (!mounted) return;

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          setState(() {
            _requests = List<Map<String, dynamic>>.from(responseData['data']);
            _isLoading = false;
          });
        } else {
          setState(() {
            _errorMessage = responseData['message'] ??
                AppLocalizations.tr(context, 'fetch_requests_failed') ??
                'Failed to fetch requests';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _errorMessage = jsonDecode(response.body)['message'] ??
              AppLocalizations.tr(context, 'fetch_requests_failed') ??
              'Failed to fetch requests';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'network_error') ?? 'Network error occurred';
        _isLoading = false;
      });
    }
  }

  Future<void> _handlePayment(BuildContext context, Map<String, dynamic> request) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 20),
                Text(AppLocalizations.tr(context, 'creating_payment') ?? 'Creating payment...'),
              ],
            ),
          );
        },
      );

      // Extract request ID and calculate amount
      final requestId = request['id']?.toString() ?? '';
      final double amount = _calculatePaymentAmount(request);

      // Create payment
      final paymentResponse = await PaymentService.createPayment(
        requestId: requestId,
        amount: amount,
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Launch payment URL
      final Uri paymentUrl = Uri.parse(paymentResponse.paymentUrl);
      if (await canLaunchUrl(paymentUrl)) {
        await launchUrl(paymentUrl, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                AppLocalizations.tr(context, 'payment_link_error') ?? 'Unable to open payment link',
              ),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment error: ${e.toString()}',
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  double _calculatePaymentAmount(Map<String, dynamic> request) {
    // Default amount - you can customize this based on request type
    // This should match your backend logic
    final String requestType = request['requestType'] ?? '';

    switch (requestType.toLowerCase()) {
      case 'building_permit':
        return 5000.0; // 5000 DZD
      case 'renovation':
        return 3000.0; // 3000 DZD
      case 'extension':
        return 4000.0; // 4000 DZD
      default:
        return 2000.0; // Default amount
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColorsWithListener(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return BaseScreen(
      currentIndex: 1,
      showAppBar: false,
      child: Column(
        children: [
          _buildHeaderSection(isArabic, context, isTablet),
          Container(
            color: colors.surface,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.primaryOrange,
              unselectedLabelColor: colors.textSecondary,
              indicatorColor: AppColors.primaryOrange,
              labelStyle: ThemeHelper.getSectionTitleStyle(context).copyWith(
                fontSize: isTablet ? 15 : 14,
                fontWeight: FontWeight.w600,
              ),
              tabs: [
                Tab(text: AppLocalizations.tr(context, 'permits') ?? 'Permits'),
                Tab(text: AppLocalizations.tr(context, 'my_requests') ?? 'My Requests'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPermitsTab(isArabic, isTablet),
                _buildRequestsTab(isArabic, context, isTablet),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(
      bool isArabic, BuildContext context, bool isTablet) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 24 : 16,
            vertical: isTablet ? 20 : 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.tr(context, 'requests') ?? 'Requests',
                style: ThemeHelper.getTitleStyle(context).copyWith(
                  color: AppColors.pureWhite,
                  fontSize: isTablet ? 24 : 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                AppLocalizations.tr(context, 'track_your_requests') ?? 'Track your requests',
                style: ThemeHelper.getSubtitleStyle(context).copyWith(
                  color: AppColors.pureWhite.withOpacity(0.8),
                  fontSize: isTablet ? 14 : 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPermitsTab(bool isArabic, bool isTablet) {
    final approvedPaidRequests = _requests.where((request) {
      return request['status'] == 'approved' && request['payment'] != null;
    }).toList();

    if (approvedPaidRequests.isEmpty) {
      return Center(
        child: Text(
          AppLocalizations.tr(context, 'no_permits') ?? 'No permits available',
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: isTablet ? 16 : 14,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 24 : 12,
        vertical: isTablet ? 16 : 12,
      ),
      child: ListView.builder(
        itemCount: approvedPaidRequests.length,
        itemBuilder: (context, index) {
          return _buildPermitCard(context, approvedPaidRequests[index], isTablet);
        },
      ),
    );
  }

  Widget _buildPermitCard(BuildContext context, Map<String, dynamic> request, bool isTablet) {
    final colors = ThemeHelper.getColors(context);
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: colors.shadowColor.withOpacity(0.06),
            blurRadius: isTablet ? 12 : 8,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: AppColors.info.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16 : 14),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(isTablet ? 14 : 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.info.withOpacity(0.15),
                    AppColors.info.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
              ),
              child: Icon(
                Icons.description,
                color: AppColors.info,
                size: isTablet ? 24 : 20,
              ),
            ),
            SizedBox(width: isTablet ? 16 : 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'permit_number')?.replaceAll('{number}', '${request['permitRequestID'] ?? 'N/A'}') ??
                        'Permit #${request['permitRequestID'] ?? 'N/A'}',
                    style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                      fontSize: isTablet ? 16 : 15,
                      fontWeight: FontWeight.w600,
                      color: colors.textPrimary,
                    ),
                  ),
                  SizedBox(height: isTablet ? 6 : 4),
                  Text(
                    AppLocalizations.tr(context, 'issue_date')?.replaceAll('{date}', request['timeStamp']?.split(' ')[0] ?? 'N/A') ??
                        'Issue Date: ${request['timeStamp']?.split(' ')[0] ?? 'N/A'}',
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      fontSize: isTablet ? 13 : 12,
                      color: colors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestsTab(bool isArabic, BuildContext context, bool isTablet) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryOrange),
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                color: AppColors.error,
                fontSize: isTablet ? 16 : 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchRequests,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryOrange,
                foregroundColor: AppColors.pureWhite,
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 24 : 20,
                  vertical: isTablet ? 12 : 10,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                ),
              ),
              child: Text(
                AppLocalizations.tr(context, 'retry') ?? 'Retry',
                style: TextStyle(
                  fontSize: isTablet ? 14 : 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (_requests.isEmpty) {
      return Center(
        child: Text(
          AppLocalizations.tr(context, 'no_requests') ?? 'No requests available',
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: isTablet ? 16 : 14,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 24 : 12,
        vertical: isTablet ? 16 : 12,
      ),
      child: ListView.builder(
        itemCount: _requests.length,
        itemBuilder: (context, index) {
          final request = _requests[index];
          if (request['status'] == 'rejected') {
            return _buildAppealCard(context, isArabic, request, isTablet);
          } else if (request['status'] == 'approved' && request['payment'] == null) {
            return _buildPaymentCard(context, isArabic, request, isTablet);
          } else {
            return _buildPendingCard(context, isArabic, request, isTablet);
          }
        },
      ),
    );
  }

  Widget _buildAppealCard(BuildContext context, bool isArabic, Map<String, dynamic> request, bool isTablet) {
    final colors = ThemeHelper.getColors(context);
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: colors.shadowColor.withOpacity(0.06),
            blurRadius: isTablet ? 12 : 8,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: AppColors.error.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16 : 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12 : 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.error.withOpacity(0.15),
                        AppColors.error.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: Icon(
                    Icons.cancel,
                    color: AppColors.error,
                    size: isTablet ? 22 : 18,
                  ),
                ),
                SizedBox(width: isTablet ? 12 : 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${AppLocalizations.tr(context, 'request_number') ?? 'Request Number'}: ${request['permitRequestID'] ?? 'N/A'}',
                        style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                          fontSize: isTablet ? 16 : 15,
                          fontWeight: FontWeight.w600,
                          color: colors.textPrimary,
                        ),
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        AppLocalizations.tr(context, request['requestType'] ?? 'unknown') ??
                            request['requestType']?.toString() ??
                            'Unknown Type',
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          fontSize: isTablet ? 13 : 12,
                          color: colors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(context, 'rejected', isTablet),
              ],
            ),
            SizedBox(height: isTablet ? 12 : 10),
            Container(
              padding: EdgeInsets.all(isTablet ? 12 : 10),
              decoration: BoxDecoration(
                color: colors.surface,
                borderRadius: BorderRadius.circular(isTablet ? 10 : 8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.square_foot,
                    color: AppColors.info,
                    size: isTablet ? 18 : 16,
                  ),
                  SizedBox(width: isTablet ? 8 : 6),
                  Text(
                    '${AppLocalizations.tr(context, 'surface') ?? 'Surface'}: ${request['totalArea']?.toString() ?? 'N/A'} m²',
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      fontSize: isTablet ? 13 : 12,
                      color: colors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: isTablet ? 12 : 10),
            _buildAppealButton(context, isArabic, request, isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentCard(BuildContext context, bool isArabic, Map<String, dynamic> request, bool isTablet) {
    final colors = ThemeHelper.getColors(context);
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: colors.shadowColor.withOpacity(0.06),
            blurRadius: isTablet ? 12 : 8,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: AppColors.success.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16 : 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12 : 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.success.withOpacity(0.15),
                        AppColors.success.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                    size: isTablet ? 22 : 18,
                  ),
                ),
                SizedBox(width: isTablet ? 12 : 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${AppLocalizations.tr(context, 'request_number') ?? 'Request Number'}: ${request['permitRequestID'] ?? 'N/A'}',
                        style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                          fontSize: isTablet ? 16 : 15,
                          fontWeight: FontWeight.w600,
                          color: colors.textPrimary,
                        ),
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        AppLocalizations.tr(context, request['requestType'] ?? 'unknown') ??
                            request['requestType']?.toString() ??
                            'Unknown Type',
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          fontSize: isTablet ? 13 : 12,
                          color: colors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(context, 'approved', isTablet),
              ],
            ),
            SizedBox(height: isTablet ? 12 : 10),
            Container(
              padding: EdgeInsets.all(isTablet ? 12 : 10),
              decoration: BoxDecoration(
                color: colors.surface,
                borderRadius: BorderRadius.circular(isTablet ? 10 : 8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.square_foot,
                    color: AppColors.info,
                    size: isTablet ? 18 : 16,
                  ),
                  SizedBox(width: isTablet ? 8 : 6),
                  Text(
                    '${AppLocalizations.tr(context, 'surface') ?? 'Surface'}: ${request['totalArea']?.toString() ?? 'N/A'} m²',
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      fontSize: isTablet ? 13 : 12,
                      color: colors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: isTablet ? 12 : 10),
            _buildPaymentButton(context, isArabic, request, isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingCard(BuildContext context, bool isArabic, Map<String, dynamic> request, bool isTablet) {
    final colors = ThemeHelper.getColors(context);
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: colors.card,
        borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
        boxShadow: [
          BoxShadow(
            color: colors.shadowColor.withOpacity(0.06),
            blurRadius: isTablet ? 12 : 8,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(
          color: AppColors.warning.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16 : 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12 : 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.warning.withOpacity(0.15),
                        AppColors.warning.withOpacity(0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
                  ),
                  child: Icon(
                    Icons.schedule,
                    color: AppColors.warning,
                    size: isTablet ? 22 : 18,
                  ),
                ),
                SizedBox(width: isTablet ? 12 : 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${AppLocalizations.tr(context, 'request_number') ?? 'Request Number'}: ${request['permitRequestID'] ?? 'N/A'}',
                        style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                          fontSize: isTablet ? 16 : 15,
                          fontWeight: FontWeight.w600,
                          color: colors.textPrimary,
                        ),
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        AppLocalizations.tr(context, request['requestType'] ?? 'unknown') ??
                            request['requestType']?.toString() ??
                            'Unknown Type',
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          fontSize: isTablet ? 13 : 12,
                          color: colors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(context, 'pending', isTablet),
              ],
            ),
            SizedBox(height: isTablet ? 12 : 10),
            Container(
              padding: EdgeInsets.all(isTablet ? 12 : 10),
              decoration: BoxDecoration(
                color: colors.surface,
                borderRadius: BorderRadius.circular(isTablet ? 10 : 8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.square_foot,
                    color: AppColors.info,
                    size: isTablet ? 18 : 16,
                  ),
                  SizedBox(width: isTablet ? 8 : 6),
                  Text(
                    '${AppLocalizations.tr(context, 'surface') ?? 'Surface'}: ${request['totalArea']?.toString() ?? 'N/A'} m²',
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(
                      fontSize: isTablet ? 13 : 12,
                      color: colors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentButton(BuildContext context, bool isArabic,
      Map<String, dynamic> request, bool isTablet) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          await _handlePayment(context, request);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.success,
          foregroundColor: AppColors.pureWhite,
          elevation: 0,
          padding: EdgeInsets.symmetric(
            vertical: isTablet ? 12 : 10,
            horizontal: isTablet ? 16 : 12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment,
              size: isTablet ? 18 : 16,
            ),
            SizedBox(width: isTablet ? 8 : 6),
            Text(
              AppLocalizations.tr(context, 'pay_fees') ?? 'Pay Fees',
              style: TextStyle(
                fontSize: isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppealButton(BuildContext context, bool isArabic,
      Map<String, dynamic> request, bool isTablet) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AppealScreen(
                requestId: request['permitRequestID']?.toString() ?? '',
                requestType: request['requestType'] ?? '',
              ),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.pureWhite,
          elevation: 0,
          padding: EdgeInsets.symmetric(
            vertical: isTablet ? 12 : 10,
            horizontal: isTablet ? 16 : 12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.gavel,
              size: isTablet ? 18 : 16,
            ),
            SizedBox(width: isTablet ? 8 : 6),
            Text(
              AppLocalizations.tr(context, 'submit_appeal') ?? 'Submit Appeal',
              style: TextStyle(
                fontSize: isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, String status, bool isTablet) {
    Color chipColor;
    String statusText;

    switch (status) {
      case 'pending':
        chipColor = AppColors.warning;
        statusText = AppLocalizations.tr(context, 'pending') ?? 'Pending';
        break;
      case 'approved':
        chipColor = AppColors.success;
        statusText = AppLocalizations.tr(context, 'approved') ?? 'Approved';
        break;
      case 'rejected':
        chipColor = AppColors.error;
        statusText = AppLocalizations.tr(context, 'rejected') ?? 'Rejected';
        break;
      default:
        chipColor = AppColors.darkGray;
        statusText = AppLocalizations.tr(context, 'unknown_status') ?? 'Unknown';
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 12 : 10,
        vertical: isTablet ? 6 : 4,
      ),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(isTablet ? 12 : 10),
        border: Border.all(
          color: chipColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 12 : 11,
        ),
      ),
    );
  }
}