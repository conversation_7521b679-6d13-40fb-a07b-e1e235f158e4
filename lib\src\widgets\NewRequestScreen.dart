import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:http_parser/http_parser.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/colors.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';

class NewRequestScreen extends StatefulWidget {
  const NewRequestScreen({super.key});

  @override
  _NewRequestScreenState createState() => _NewRequestScreenState();
}

class _NewRequestScreenState extends State<NewRequestScreen> {
  int _currentStep = 1;
  bool _isOwnerApplicant = false;
  String? _propertyType;
  String? _requestType;
  final TextEditingController _ownerNameController = TextEditingController();
  final TextEditingController _ownerAddressController = TextEditingController();
  final TextEditingController _ownerEmailController = TextEditingController();
  final TextEditingController _ownerPhoneNumberController = TextEditingController();
  final TextEditingController _projectAddressController = TextEditingController();
  final TextEditingController _totalAreaController = TextEditingController();
  final TextEditingController _requestNatureController = TextEditingController();
  final TextEditingController _currentLandUseController = TextEditingController();
  final TextEditingController _existingBuildingsController = TextEditingController();
  final TextEditingController _suggestedDurationController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  PlatformFile? _selectedFile;
  final _storage = const FlutterSecureStorage();
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic>? _userProfile;

  final Dio _dio = Dio()
    ..interceptors.add(InterceptorsWrapper(
      onError: (DioException e, handler) {
        print('Dio Error: ${e.message}, Response: ${e.response?.data}');
        return handler.next(e);
      },
    ));

  @override
  void initState() {
    super.initState();
    _fetchUserProfile();
  }

  Future<void> _fetchUserProfile() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null || token.isEmpty) {
        return;
      }

      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/profile'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['data'] != null) {
          setState(() {
            _userProfile = data['data'];
          });
        }
      }
    } catch (e) {
      // Silently handle error - user profile is optional
    }
  }

  void _updateOwnerFields() {
    if (_isOwnerApplicant && _userProfile != null) {
      // When applicant is owner, populate fields with user data for display
      _ownerNameController.text = _userProfile!['name'] ?? '';
      _ownerPhoneNumberController.text = _userProfile!['phoneNumber'] ?? '';
      _ownerEmailController.text = _userProfile!['email'] ?? '';
      _ownerAddressController.text = _userProfile!['address'] ?? '';
    } else if (!_isOwnerApplicant) {
      // When applicant is not owner, clear fields for manual entry
      _ownerNameController.clear();
      _ownerPhoneNumberController.clear();
      _ownerEmailController.clear();
      _ownerAddressController.clear();
    }
  }

  @override
  void dispose() {
    _ownerNameController.dispose();
    _ownerAddressController.dispose();
    _ownerEmailController.dispose();
    _ownerPhoneNumberController.dispose();
    _projectAddressController.dispose();
    _totalAreaController.dispose();
    _requestNatureController.dispose();
    _currentLandUseController.dispose();
    _existingBuildingsController.dispose();
    _suggestedDurationController.dispose();
    super.dispose();
  }

  void _nextStep() {
    final formState = _formKey.currentState;
    if (formState != null && formState.validate()) {
      if (_currentStep == 3 && _selectedFile == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'no_file_selected')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      if (_currentStep < 4) {
        setState(() {
          _currentStep += 1;
        });
      } else {
        _showSubmitConfirmation();
      }
    }
  }

  void _previousStep() {
    setState(() {
      if (_currentStep > 1) {
        _currentStep -= 1;
      }
    });
  }

  Future<void> _pickFile() async {
    if (!mounted) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        withData: true,
      );

      if (!mounted) return;

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.size > 5 * 1024 * 1024) { // Updated to 5MB to match backend
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'file_too_large')),
              backgroundColor: AppColors.error,
            ),
          );
          return;
        }
        print('Selected File: ${file.name}, Size: ${file.size} bytes');
        setState(() {
          _selectedFile = file;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'file_selected_success')),
            backgroundColor: AppColors.success,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'no_file_selected')),
            backgroundColor: AppColors.warning,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      print('File Picker Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.tr(context, 'file_pick_error')}: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _submitForm() async {
    if (_selectedFile == null || _selectedFile!.bytes == null) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'no_file_uploaded');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'no_file_uploaded')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Validate owner information based on whether applicant is owner
    if (!_isOwnerApplicant) {
      // When applicant is not owner, owner information must be manually entered
      if (_ownerNameController.text.trim().isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'enter_owner_name');
          _isLoading = false;
        });
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'enter_owner_name')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      if (_ownerPhoneNumberController.text.trim().isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'enter_owner_phone_number');
          _isLoading = false;
        });
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'enter_owner_phone_number')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
    } else {
      // When applicant is owner, validate that user profile information is available
      if (_userProfile == null ||
          _userProfile!['name'] == null ||
          _userProfile!['name'].toString().trim().isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'user_profile_incomplete');
          _isLoading = false;
        });
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'user_profile_incomplete')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      if (_userProfile!['phoneNumber'] == null ||
          _userProfile!['phoneNumber'].toString().trim().isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'user_phone_missing');
          _isLoading = false;
        });
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'user_phone_missing')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
    }
    if (_projectAddressController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'enter_project_address');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'enter_project_address')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }
    if (_totalAreaController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'enter_total_area');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'enter_total_area')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }
    if (_requestType == null) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'select_request_type');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'select_request_type')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }
    if (_propertyType == null) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'select_project_type');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'select_project_type')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }
    if (_suggestedDurationController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'enter_suggested_duration');
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'enter_suggested_duration')),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null || token.isEmpty) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'not_authenticated');
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'not_authenticated')),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Convert suggested duration from years to months
      final suggestedDurationYears = int.tryParse(_suggestedDurationController.text.trim()) ?? 1;
      final suggestedDurationMonths = suggestedDurationYears;

      // Prepare owner information
      String ownerName;
      String ownerPhoneNumber;

      if (_isOwnerApplicant) {
        // Use current user's information
        ownerName = _userProfile?['name'] ?? '';
        ownerPhoneNumber = _userProfile?['phoneNumber'] ?? '';
      } else {
        // Use manually entered owner information
        ownerName = _ownerNameController.text.trim();
        ownerPhoneNumber = _ownerPhoneNumberController.text.trim();
      }

      final formData = FormData.fromMap({
        'projectAddress': _projectAddressController.text.trim(),
        'requestType': _requestType!, // Use the value directly as it's already in correct format
        'totalArea': _totalAreaController.text.trim(),
        'isSenderOwner': _isOwnerApplicant ? '1' : '0',
        'projectType': _propertyType!, // Ensure non-null
        'landUse': _currentLandUseController.text.trim().isEmpty
            ? 'residential'
            : _currentLandUseController.text.trim(),
        'buildingDetails': _existingBuildingsController.text.trim().isEmpty
            ? '2-story house'
            : _existingBuildingsController.text.trim(),
        'ownerName': ownerName,
        'ownerPhoneNumber': ownerPhoneNumber,
        'suggestedDuration': suggestedDurationMonths.toString(), // Send duration in months
        'pdf': MultipartFile.fromBytes(
          _selectedFile!.bytes!,
          filename: _selectedFile!.name,
          contentType: MediaType('application', 'pdf'),
        ),
      });

      print('Sending formData: ${formData.fields}'); // Debug log

      final response = await _dio.post(
        'https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/request',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
          },
          validateStatus: (status) => status! < 500,
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
      );

      print('Response: ${response.statusCode}, ${response.data}'); // Debug log

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data;
        if (responseData is Map && responseData['success'] == true) {
          await Future.delayed(const Duration(milliseconds: 100));
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'form_submitted_success')),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        } else {
          setState(() {
            _errorMessage = AppLocalizations.tr(context, 'no_response_data') +
                ': ${response.data.toString()}';
            _isLoading = false;
          });
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_errorMessage!),
              backgroundColor: AppColors.error,
            ),
          );
        }
      } else {
        String errorMsg = AppLocalizations.tr(context, 'submission_failed');
        if (response.data is Map) {
          if (response.data['message'] != null) {
            errorMsg = response.data['message'].toString();
            if (response.data['errors'] != null) {
              errorMsg += ': ${response.data['errors'].toString()}';
            }
          } else if (response.statusCode == 401) {
            errorMsg = AppLocalizations.tr(context, 'invalid_token');
          } else if (response.statusCode == 422) {
            errorMsg = AppLocalizations.tr(context, 'invalid_form_data');
            if (response.data['errors'] != null) {
              errorMsg += ': ${response.data['errors'].toString()}';
            }
          } else if (response.statusCode == 400) {
            errorMsg = AppLocalizations.tr(context, 'bad_request');
          }
        }
        setState(() {
          _errorMessage = '$errorMsg (Status: ${response.statusCode})';
          _isLoading = false;
        });
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } on DioException catch (e) {
      print('DioException: ${e.response?.statusCode}, ${e.response?.data}, ${e.requestOptions.uri}');
      String errorMsg = AppLocalizations.tr(context, 'network_error');
      if (e.response?.data is Map) {
        errorMsg = e.response!.data['message']?.toString() ?? errorMsg;
        if (e.response!.data['errors'] != null) {
          errorMsg += ': ${e.response!.data['errors'].toString()}';
        }
      }
      setState(() {
        _errorMessage = errorMsg;
        _isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: AppColors.error,
        ),
      );
    } catch (e) {
      print('Unexpected Error: $e, StackTrace: ${StackTrace.current}');
      setState(() {
        _errorMessage = '${AppLocalizations.tr(context, 'network_error')}: $e';
        _isLoading = false;
      });
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _showSubmitConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
        final isArabic = localeProvider.locale.languageCode == 'ar';
        final screenWidth = MediaQuery.of(context).size.width;
        final isTablet = screenWidth > 600;

        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryOrange.withOpacity(0.2),
                      AppColors.primaryOrange.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.send,
                  color: AppColors.primaryOrange,
                  size: isTablet ? 28 : 24,
                ),
              ),
              SizedBox(width: isTablet ? 12 : 10),
              Expanded(
                child: Text(
                  AppLocalizations.tr(context, 'submit_confirmation_title'),
                  style: TextStyle(
                    fontSize: isTablet ? 20 : 18,
                    fontWeight: FontWeight.bold,
                    color: ThemeHelper.getColors(context).textPrimary,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.tr(context, 'submit_confirmation_message'),
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  color: ThemeHelper.getColors(context).textSecondary,
                ),
              ),
              SizedBox(height: isTablet ? 16 : 12),
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.info.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.info,
                      size: isTablet ? 20 : 18,
                    ),
                    SizedBox(width: isTablet ? 8 : 6),
                    Expanded(
                      child: Text(
                        AppLocalizations.tr(context, 'submit_warning_message'),
                        style: TextStyle(
                          fontSize: isTablet ? 13 : 12,
                          color: AppColors.info,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                AppLocalizations.tr(context, 'cancel'),
                style: TextStyle(
                  color: ThemeHelper.getColors(context).textSecondary,
                  fontSize: isTablet ? 14 : 13,
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryOrange,
                    AppColors.primaryOrange.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ElevatedButton(
                onPressed: _isLoading
                    ? null
                    : () {
                        Navigator.of(context).pop();
                        _submitForm();
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: AppColors.pureWhite,
                  elevation: 0,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.pureWhite),
                      )
                    : Text(
                        AppLocalizations.tr(context, 'submit'),
                        style: TextStyle(
                          fontSize: isTablet ? 14 : 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: ThemeHelper.getColors(context).backgroundPrimary,
      body: Column(
        children: [
          _buildSimpleHeader(context, isArabic, isTablet),
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isTablet ? 16 : 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSimpleProgressIndicator(isArabic, isTablet),
                    SizedBox(height: isTablet ? 16 : 12),
                    _buildStepContent(isArabic, isTablet),
                    if (_errorMessage != null) ...[
                      SizedBox(height: isTablet ? 16 : 12),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: AppColors.error,
                          fontSize: isTablet ? 14 : 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                    SizedBox(height: isTablet ? 20 : 16),
                    _buildSimpleActionButtons(isArabic, context, isTablet),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleHeader(
      BuildContext context, bool isArabic, bool isTablet) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryOrange,
            AppColors.primaryOrange.withOpacity(0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 16 : 12,
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(
                  isArabic ? Icons.arrow_forward : Icons.arrow_back,
                  color: AppColors.pureWhite,
                  size: isTablet ? 24 : 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              SizedBox(width: isTablet ? 12 : 8),
              Expanded(
                child: Text(
                  AppLocalizations.tr(context, 'submit_request'),
                  style: TextStyle(
                    fontSize: isTablet ? 20 : 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.pureWhite,
                  ),
                  textAlign: isArabic ? TextAlign.right : TextAlign.left,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleProgressIndicator(bool isArabic, bool isTablet) {
    final steps = ['Owner', 'Project', 'Documents', 'Summary'];

    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getColors(context).card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeHelper.getColors(context).shadowColor.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: List.generate(4, (index) {
          final stepNumber = index + 1;
          final isActive = stepNumber == _currentStep;
          final isCompleted = stepNumber < _currentStep;

          return Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: isTablet ? 36 : 32,
                    decoration: BoxDecoration(
                      gradient: isActive || isCompleted
                          ? LinearGradient(
                              colors: [
                                AppColors.primaryOrange,
                                AppColors.primaryOrange.withOpacity(0.8),
                              ],
                            )
                          : null,
                      color: isActive || isCompleted
                          ? null
                          : ThemeHelper.getColors(context).surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isActive || isCompleted
                            ? AppColors.primaryOrange
                            : ThemeHelper.getColors(context).borderPrimary,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: isTablet ? 20 : 18,
                            height: isTablet ? 20 : 18,
                            decoration: BoxDecoration(
                              color: isActive || isCompleted
                                  ? AppColors.pureWhite
                                  : ThemeHelper.getColors(context)
                                      .textSecondary,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: isCompleted && !isActive
                                  ? Icon(
                                      Icons.check,
                                      color: AppColors.primaryOrange,
                                      size: isTablet ? 12 : 10,
                                    )
                                  : Text(
                                      stepNumber.toString(),
                                      style: TextStyle(
                                        color: isActive || isCompleted
                                            ? AppColors.primaryOrange
                                            : AppColors.pureWhite,
                                        fontWeight: FontWeight.bold,
                                        fontSize: isTablet ? 10 : 9,
                                      ),
                                    ),
                            ),
                          ),
                          if (isTablet) ...[
                            SizedBox(width: isTablet ? 6 : 4),
                            Flexible(
                              child: Text(
                                steps[index],
                                style: TextStyle(
                                  color: isActive || isCompleted
                                      ? AppColors.pureWhite
                                      : ThemeHelper.getColors(context)
                                          .textSecondary,
                                  fontWeight: FontWeight.w500,
                                  fontSize: isTablet ? 11 : 10,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
                if (index < 3)
                  Container(
                    width: isTablet ? 8 : 6,
                    height: 2,
                    margin: EdgeInsets.symmetric(horizontal: isTablet ? 6 : 4),
                    color: stepNumber < _currentStep
                        ? AppColors.primaryOrange
                        : ThemeHelper.getColors(context).borderPrimary,
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStepContent(bool isArabic, bool isTablet) {
    switch (_currentStep) {
      case 1:
        return _buildGeneralOwnerInfo(isArabic, isTablet);
      case 2:
        return _buildProjectInfo(isArabic, isTablet);
      case 3:
        return _buildStep3Documents(isArabic, isTablet);
      case 4:
        return _buildStep4Summary(isArabic, isTablet);
      default:
        return Container();
    }
  }

  Widget _buildSimpleActionButtons(
      bool isArabic, BuildContext context, bool isTablet) {
    return Row(
      children: [
        if (_currentStep > 1)
          Expanded(
            child: Container(
              height: isTablet ? 42 : 38,
              decoration: BoxDecoration(
                color: ThemeHelper.getColors(context).card,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ThemeHelper.getColors(context).borderPrimary,
                  width: 1,
                ),
              ),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _previousStep,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: ThemeHelper.getColors(context).textPrimary,
                  elevation: 0,
                  shadowColor: Colors.transparent,
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 12 : 8,
                    vertical: isTablet ? 8 : 6,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (!isArabic) ...[
                      Icon(
                        Icons.arrow_back,
                        size: isTablet ? 16 : 14,
                      ),
                      SizedBox(width: isTablet ? 4 : 3),
                    ],
                    Flexible(
                      child: Text(
                        AppLocalizations.tr(context, 'previous'),
                        style: TextStyle(
                          fontSize: isTablet ? 13 : 11,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (isArabic) ...[
                      SizedBox(width: isTablet ? 4 : 3),
                      Icon(
                        Icons.arrow_forward,
                        size: isTablet ? 16 : 14,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        if (_currentStep > 1) SizedBox(width: isTablet ? 10 : 8),
        Expanded(
          flex: _currentStep > 1 ? 2 : 1,
          child: Container(
            height: isTablet ? 42 : 38,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryOrange,
                  AppColors.primaryOrange.withOpacity(0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryOrange.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: AppColors.pureWhite,
                elevation: 0,
                shadowColor: Colors.transparent,
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 12 : 8,
                  vertical: isTablet ? 8 : 6,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!isArabic) ...[
                    Flexible(
                      child: Text(
                        _currentStep < 4
                            ? AppLocalizations.tr(context, 'next')
                            : AppLocalizations.tr(context, 'submit'),
                        style: TextStyle(
                          fontSize: isTablet ? 13 : 11,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: isTablet ? 4 : 3),
                    Icon(
                      _currentStep < 4 ? Icons.arrow_forward : Icons.send,
                      size: isTablet ? 16 : 14,
                    ),
                  ] else ...[
                    Icon(
                      _currentStep < 4 ? Icons.arrow_back : Icons.send,
                      size: isTablet ? 16 : 14,
                    ),
                    SizedBox(width: isTablet ? 4 : 3),
                    Flexible(
                      child: Text(
                        _currentStep < 4
                            ? AppLocalizations.tr(context, 'next')
                            : AppLocalizations.tr(context, 'submit'),
                        style: TextStyle(
                          fontSize: isTablet ? 13 : 11,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGeneralOwnerInfo(bool isArabic, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getColors(context).card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeHelper.getColors(context).shadowColor.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 8 : 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryOrange.withOpacity(0.2),
                      AppColors.primaryOrange.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.person_outline,
                  color: AppColors.primaryOrange,
                  size: isTablet ? 20 : 18,
                ),
              ),
              SizedBox(width: isTablet ? 12 : 8),
              Text(
                AppLocalizations.tr(context, 'owner_info'),
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getColors(context).textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: isTablet ? 16 : 12),
          Container(
            padding: EdgeInsets.all(isTablet ? 12 : 10),
            decoration: BoxDecoration(
              color: ThemeHelper.getColors(context).surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: ThemeHelper.getColors(context).borderPrimary,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _isOwnerApplicant,
                  onChanged: (value) {
                    setState(() {
                      _isOwnerApplicant = value ?? false;
                    });
                    _updateOwnerFields();
                  },
                  activeColor: AppColors.primaryOrange,
                ),
                SizedBox(width: isTablet ? 8 : 6),
                Expanded(
                  child: Text(
                    AppLocalizations.tr(context, 'applicant_is_owner'),
                    style: TextStyle(
                      fontSize: isTablet ? 14 : 12,
                      fontWeight: FontWeight.w500,
                      color: ThemeHelper.getColors(context).textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Always show owner fields, but make them read-only when applicant is owner
          SizedBox(height: isTablet ? 16 : 12),
          _buildSimpleTextField(
            controller: _ownerNameController,
            label: AppLocalizations.tr(context, 'owner_name'),
            icon: Icons.person,
            isArabic: isArabic,
            isTablet: isTablet,
            isReadOnly: _isOwnerApplicant,
            validator: (value) => (value == null || value.isEmpty)
                ? AppLocalizations.tr(context, 'enter_owner_name')
                : null,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _ownerAddressController,
            label: AppLocalizations.tr(context, 'owner_address'),
            icon: Icons.location_on,
            isArabic: isArabic,
            isTablet: isTablet,
            isReadOnly: _isOwnerApplicant,
            validator: !_isOwnerApplicant ? (value) => (value == null || value.isEmpty)
                ? AppLocalizations.tr(context, 'enter_owner_address')
                : null : null,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _ownerPhoneNumberController,
            label: AppLocalizations.tr(context, 'owner_phone_number'),
            icon: Icons.phone,
            isArabic: isArabic,
            isTablet: isTablet,
            isReadOnly: _isOwnerApplicant,
            keyboardType: TextInputType.phone,
            validator: (value) => (value == null || value.isEmpty)
                ? AppLocalizations.tr(context, 'enter_owner_phone_number')
                : null,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _ownerEmailController,
            label: AppLocalizations.tr(context, 'owner_email'),
            icon: Icons.email,
            isArabic: isArabic,
            isTablet: isTablet,
            isReadOnly: _isOwnerApplicant,
            keyboardType: TextInputType.emailAddress,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required bool isArabic,
    bool isTablet = false,
    bool isReadOnly = false,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int? maxLines = 1,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        readOnly: isReadOnly,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        maxLines: maxLines,
        style: TextStyle(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w400,
          color: isReadOnly ? Colors.grey[600] : Colors.black,
        ),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: label,
          prefixIcon: Icon(
            icon,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
      ),
    );
  }

  Widget _buildProjectInfo(bool isArabic, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: ThemeHelper.getColors(context).card,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeHelper.getColors(context).shadowColor.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 8 : 6),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryOrange.withOpacity(0.2),
                      AppColors.primaryOrange.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.business_outlined,
                  color: AppColors.primaryOrange,
                  size: isTablet ? 20 : 18,
                ),
              ),
              SizedBox(width: isTablet ? 12 : 8),
              Text(
                AppLocalizations.tr(context, 'project_info'),
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getColors(context).textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: isTablet ? 16 : 12),
          _buildSimpleTextField(
            controller: _projectAddressController,
            label: AppLocalizations.tr(context, 'project_address'),
            icon: Icons.location_on,
            isArabic: isArabic,
            isTablet: isTablet,
            validator: (value) => (value == null || value.isEmpty)
                ? AppLocalizations.tr(context, 'enter_project_address')
                : null,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _totalAreaController,
            label: AppLocalizations.tr(context, 'total_area'),
            icon: Icons.square_foot,
            isArabic: isArabic,
            isTablet: isTablet,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.tr(context, 'enter_total_area');
              }
              if (double.tryParse(value) == null || double.parse(value) <= 0) {
                return AppLocalizations.tr(context, 'invalid_area');
              }
              return null;
            },
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleDropdown(
            value: _propertyType,
            label: AppLocalizations.tr(context, 'project_type'),
            icon: Icons.home_work,
            items: ['residential', 'commercial', 'industrial', 'other'],
            onChanged: (value) => setState(() => _propertyType = value),
            isArabic: isArabic,
            isTablet: isTablet,
            validationKey: 'select_project_type',
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleDropdown(
            value: _requestType,
            label: AppLocalizations.tr(context, 'request_type'),
            icon: Icons.construction,
            items: ['individual', 'non_individual'], // Updated to match backend expectations
            onChanged: (value) => setState(() => _requestType = value),
            isArabic: isArabic,
            isTablet: isTablet,
            validationKey: 'select_request_type',
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _requestNatureController,
            label: AppLocalizations.tr(context, 'request_nature'),
            icon: Icons.description,
            isArabic: isArabic,
            isTablet: isTablet,
            validator: (value) => (value == null || value.isEmpty)
                ? AppLocalizations.tr(context, 'enter_request_nature')
                : null,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _currentLandUseController,
            label: AppLocalizations.tr(context, 'current_land_use'),
            icon: Icons.landscape,
            isArabic: isArabic,
            isTablet: isTablet,
            validator: (value) => value == null || value.isEmpty
                ? AppLocalizations.tr(context, 'enter_current_land_use')
                : null,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _existingBuildingsController,
            label: AppLocalizations.tr(context, 'existing_buildings'),
            icon: Icons.apartment,
            isArabic: isArabic,
            isTablet: isTablet,
            validator: (value) => value == null || value.isEmpty
                ? AppLocalizations.tr(context, 'enter_existing_buildings')
                : null,
            maxLines: 3,
          ),
          SizedBox(height: isTablet ? 12 : 10),
          _buildSimpleTextField(
            controller: _suggestedDurationController,
            label: '${AppLocalizations.tr(context, 'suggested_duration')} (${AppLocalizations.tr(context, 'years')})',
            icon: Icons.calendar_today,
            isArabic: isArabic,
            isTablet: isTablet,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppLocalizations.tr(context, 'enter_suggested_duration');
              }
              if (int.tryParse(value) == null || int.parse(value) <= 0) {
                return AppLocalizations.tr(context, 'invalid_duration');
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleDropdown({
    required String? value,
    required String label,
    required IconData icon,
    required List<String> items,
    required Function(String?) onChanged,
    required bool isArabic,
    required bool isTablet,
    String? validationKey,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        validator: validationKey != null
            ? (value) => value == null
                ? AppLocalizations.tr(context, validationKey)
                : null
            : null,
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: label,
          prefixIcon: Icon(
            icon,
            color: AppColors.primaryOrange.withOpacity(0.7),
            size: isTablet ? 24 : 20,
          ),
        ).copyWith(
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTablet ? 20 : 16,
            vertical: isTablet ? 18 : 16,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(isTablet ? 16 : 14),
            borderSide: BorderSide(
              color: AppColors.primaryOrange,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: ThemeHelper.getColors(context).surface,
        ),
        items: items.map((item) {
          String translationKey;
          if (label.toLowerCase().contains('project')) {
            translationKey = 'project_type_$item';
          } else if (label.toLowerCase().contains('request')) {
            translationKey = 'request_type_${item.toLowerCase()}';
          } else {
            translationKey = item;
          }

          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              AppLocalizations.tr(context, translationKey),
              style: TextStyle(
                fontSize: isTablet ? 16 : 14,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        style: TextStyle(
          fontSize: isTablet ? 14 : 13,
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStep3Documents(bool isArabic, bool isTablet) {
    final hasFile = _selectedFile != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'documents'),
          style: TextStyle(
            fontSize: isTablet ? 20 : 18,
            fontWeight: FontWeight.bold,
            color: ThemeHelper.getColors(context).textPrimary,
          ),
        ),
        SizedBox(height: isTablet ? 16 : 12),
        Container(
          padding: EdgeInsets.all(isTablet ? 20 : 16),
          decoration: BoxDecoration(
            color: ThemeHelper.getColors(context).surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: ThemeHelper.getColors(context).shadowColor.withOpacity(0.06),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isTablet ? 12 : 10),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryOrange.withOpacity(0.2),
                          AppColors.primaryOrange.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.cloud_upload_outlined,
                      color: AppColors.primaryOrange,
                      size: isTablet ? 24 : 20,
                    ),
                  ),
                  SizedBox(width: isTablet ? 16 : 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.tr(context, 'upload_file'),
                          style: TextStyle(
                            fontSize: isTablet ? 18 : 16,
                            fontWeight: FontWeight.bold,
                            color: ThemeHelper.getColors(context).textPrimary,
                          ),
                        ),
                        Text(
                          AppLocalizations.tr(context, 'pdf_only'),
                          style: TextStyle(
                            fontSize: isTablet ? 12 : 11,
                            color: ThemeHelper.getColors(context).textSecondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: isTablet ? 20 : 16),
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.info.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.tr(context, 'important_documents_remark'),
                      style: TextStyle(
                        fontSize: isTablet ? 14 : 13,
                        fontWeight: FontWeight.w600,
                        color: AppColors.info,
                      ),
                    ),
                    SizedBox(height: isTablet ? 8 : 6),
                    Text(
                      '${AppLocalizations.tr(context, 'required')}: ${AppLocalizations.tr(context, 'document_title_deed')}, ${AppLocalizations.tr(context, 'document_building_plans')}',
                      style: TextStyle(
                        fontSize: isTablet ? 13 : 12,
                        color: AppColors.error,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${AppLocalizations.tr(context, 'optional')}: ${AppLocalizations.tr(context, 'document_power_of_attorney')}, ${AppLocalizations.tr(context, 'document_agricultural_authorization')}, ${AppLocalizations.tr(context, 'document_commercial_register')}',
                      style: TextStyle(
                        fontSize: isTablet ? 13 : 12,
                        color: ThemeHelper.getColors(context).textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: isTablet ? 20 : 16),
              Container(
                padding: EdgeInsets.all(isTablet ? 16 : 12),
                decoration: BoxDecoration(
                  color: hasFile
                      ? AppColors.success.withOpacity(0.05)
                      : ThemeHelper.getColors(context).surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: hasFile
                        ? AppColors.success.withOpacity(0.3)
                        : AppColors.primaryOrange.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ThemeHelper.getColors(context)
                          .shadowColor
                          .withOpacity(0.06),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(isTablet ? 10 : 8),
                          decoration: BoxDecoration(
                            color: hasFile
                                ? AppColors.success.withOpacity(0.15)
                                : AppColors.primaryOrange.withOpacity(0.15),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.description,
                            color: hasFile
                                ? AppColors.success
                                : AppColors.primaryOrange,
                            size: isTablet ? 20 : 18,
                          ),
                        ),
                        SizedBox(width: isTablet ? 12 : 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.tr(context, 'all_documents'),
                                style: TextStyle(
                                  fontSize: isTablet ? 14 : 13,
                                  fontWeight: FontWeight.w600,
                                  color:
                                      ThemeHelper.getColors(context).textPrimary,
                                ),
                              ),
                              if (hasFile) ...[
                                SizedBox(height: isTablet ? 6 : 4),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: AppColors.success,
                                      size: isTablet ? 16 : 14,
                                    ),
                                    SizedBox(width: isTablet ? 6 : 4),
                                    Expanded(
                                      child: Text(
                                        _selectedFile!.name,
                                        style: TextStyle(
                                          fontSize: isTablet ? 12 : 11,
                                          color: AppColors.success,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: isTablet ? 12 : 10),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: isTablet ? 38 : 34,
                            decoration: BoxDecoration(
                              gradient: hasFile
                                  ? LinearGradient(
                                      colors: [
                                        AppColors.success,
                                        AppColors.success.withOpacity(0.8),
                                      ],
                                    )
                                  : LinearGradient(
                                      colors: [
                                        AppColors.primaryOrange,
                                        AppColors.primaryOrange.withOpacity(0.8),
                                      ],
                                    ),
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: (hasFile
                                          ? AppColors.success
                                          : AppColors.primaryOrange)
                                      .withOpacity(0.3),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _pickFile,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                foregroundColor: AppColors.pureWhite,
                                elevation: 0,
                                shadowColor: Colors.transparent,
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 12 : 8,
                                  vertical: isTablet ? 8 : 6,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    hasFile ? Icons.refresh : Icons.upload_file,
                                    size: isTablet ? 16 : 14,
                                  ),
                                  SizedBox(width: isTablet ? 6 : 4),
                                  Flexible(
                                    child: Text(
                                      hasFile
                                          ? AppLocalizations.tr(
                                              context, 'replace_file')
                                          : AppLocalizations.tr(
                                              context, 'select_file_button'),
                                      style: TextStyle(
                                        fontSize: isTablet ? 13 : 11,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        if (hasFile) ...[
                          SizedBox(width: isTablet ? 12 : 8),
                          Container(
                            height: isTablet ? 38 : 34,
                            width: isTablet ? 38 : 34,
                            decoration: BoxDecoration(
                              color: AppColors.error.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: AppColors.error.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: IconButton(
                              onPressed: () => setState(() => _selectedFile = null),
                              icon: Icon(
                                Icons.delete_outline,
                                color: AppColors.error,
                                size: isTablet ? 20 : 18,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStep4Summary(bool isArabic, bool isTablet) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'summary'),
          style: TextStyle(
            fontSize: isTablet ? 20 : 18,
            fontWeight: FontWeight.bold,
            color: ThemeHelper.getColors(context).textPrimary,
          ),
        ),
        SizedBox(height: isTablet ? 16 : 12),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isTablet ? 20 : 16),
          decoration: BoxDecoration(
            color: ThemeHelper.getColors(context).surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: ThemeHelper.getColors(context)
                    .shadowColor
                    .withOpacity(0.06),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.tr(context, 'review_information'),
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeHelper.getColors(context).textPrimary,
                ),
              ),
              SizedBox(height: isTablet ? 16 : 12),
              _buildSummarySection(
                title: AppLocalizations.tr(context, 'owner_info'),
                items: [
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'applicant_is_owner'),
                    value: _isOwnerApplicant
                        ? AppLocalizations.tr(context, 'yes')
                        : AppLocalizations.tr(context, 'no'),
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  if (!_isOwnerApplicant) ...[
                    _buildSummaryItem(
                      label: AppLocalizations.tr(context, 'owner_name'),
                      value: _ownerNameController.text,
                      isArabic: isArabic,
                      isTablet: isTablet,
                    ),
                    _buildSummaryItem(
                      label: AppLocalizations.tr(context, 'owner_address'),
                      value: _ownerAddressController.text,
                      isArabic: isArabic,
                      isTablet: isTablet,
                    ),
                    _buildSummaryItem(
                      label: AppLocalizations.tr(context, 'owner_phone_number'),
                      value: _ownerPhoneNumberController.text,
                      isArabic: isArabic,
                      isTablet: isTablet,
                    ),
                    _buildSummaryItem(
                      label: AppLocalizations.tr(context, 'owner_email'),
                      value: _ownerEmailController.text,
                      isArabic: isArabic,
                      isTablet: isTablet,
                    ),
                  ],
                ],
                isArabic: isArabic,
                isTablet: isTablet,
              ),
              SizedBox(height: isTablet ? 20 : 16),
              _buildSummarySection(
                title: AppLocalizations.tr(context, 'project_info'),
                items: [
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'project_address'),
                    value: _projectAddressController.text,
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'total_area'),
                    value: _totalAreaController.text,
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'project_type'),
                    value: _propertyType != null
                        ? AppLocalizations.tr(context, 'project_type_$_propertyType')
                        : AppLocalizations.tr(context, 'not_selected'),
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'request_type'),
                    value: _requestType != null
                        ? AppLocalizations.tr(context, 'request_type_${_requestType!.toLowerCase()}')
                        : AppLocalizations.tr(context, 'not_selected'),
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'request_nature'),
                    value: _requestNatureController.text,
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'current_land_use'),
                    value: _currentLandUseController.text.isEmpty
                        ? '-'
                        : _currentLandUseController.text,
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'existing_buildings'),
                    value: _existingBuildingsController.text.isEmpty
                        ? '-'
                        : _existingBuildingsController.text,
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'suggested_duration'),
                    value: _suggestedDurationController.text.isEmpty
                        ? '-'
                        : '${_suggestedDurationController.text} ${AppLocalizations.tr(context, 'years')} (${(int.tryParse(_suggestedDurationController.text) ?? 1) * 12} ${AppLocalizations.tr(context, 'months')})',
                    isArabic: isArabic,
                    isTablet: isTablet,
                  ),
                ],
                isArabic: isArabic,
                isTablet: isTablet,
              ),
              SizedBox(height: isTablet ? 20 : 16),
              _buildSummarySection(
                title: AppLocalizations.tr(context, 'documents'),
                items: [
                  _buildSummaryItem(
                    label: AppLocalizations.tr(context, 'attached_file'),
                    value: _selectedFile != null
                        ? _selectedFile!.name
                        : AppLocalizations.tr(context, 'no_file'),
                    isArabic: isArabic,
                    isTablet: isTablet,
                    isHighlight: _selectedFile == null,
                    icon: _selectedFile != null ? Icons.picture_as_pdf : null,
                  ),
                ],
                isArabic: isArabic,
                isTablet: isTablet,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummarySection({
    required String title,
    required List<Widget> items,
    required bool isArabic,
    required bool isTablet,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryOrange,
          ),
        ),
        SizedBox(height: isTablet ? 12 : 8),
        ...items,
      ],
    );
  }

  Widget _buildSummaryItem({
    required String label,
    required String value,
    required bool isArabic,
    required bool isTablet,
    IconData? icon,
    bool isHighlight = false,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 10 : 8),
      padding: EdgeInsets.all(isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: isHighlight
            ? AppColors.error.withOpacity(0.05)
            : ThemeHelper.getColors(context)
                .backgroundSecondary
                .withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isHighlight
              ? AppColors.error.withOpacity(0.3)
              : ThemeHelper.getColors(context).borderPrimary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: isTablet ? 18 : 16,
              color: AppColors.error,
            ),
            SizedBox(width: isTablet ? 8 : 6),
          ],
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: isTablet ? 13 : 12,
                fontWeight: FontWeight.w600,
                color: isHighlight
                    ? AppColors.error
                    : ThemeHelper.getColors(context).textPrimary,
              ),
            ),
          ),
          SizedBox(width: isTablet ? 12 : 8),
          Expanded(
            flex: 3,
            child: Text(
              value.isEmpty ? '-' : value,
              style: TextStyle(
                fontSize: isTablet ? 13 : 12,
                color: isHighlight
                    ? AppColors.error
                    : ThemeHelper.getColors(context).textSecondary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: isArabic ? TextAlign.right : TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }
}